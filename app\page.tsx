import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Gem, 
  Gift, 
  Lock, 
  Zap, 
  Award, 
  Clock, 
  ArrowRight, 
  Star, 
  MessageCircle, 
  Heart,
  ShoppingCart,
  Sparkles,
  Crown,
  Box,
  Coins,
  Package
} from 'lucide-react'

export default function HomePage() {
  const categories = [
    { name: 'Skins', icon: Sparkles, color: 'bg-gradient-to-r from-purple-600 to-violet-600' },
    { name: 'Chromas', icon: Crown, color: 'bg-gradient-to-r from-fuchsia-600 to-pink-600' },
    { name: 'Passes', icon: Award, color: 'bg-gradient-to-r from-emerald-600 to-teal-600' },
    { name: 'Chests', icon: Box, color: 'bg-gradient-to-r from-orange-600 to-red-600' },
    { name: 'Orbs', icon: Coins, color: 'bg-gradient-to-r from-blue-600 to-indigo-600' },
    { name: 'Bund<PERSON>', icon: Package, color: 'bg-gradient-to-r from-yellow-600 to-orange-600' },
    { name: 'TFT', icon: Gem, color: 'bg-gradient-to-r from-cyan-600 to-blue-600' }
  ]

  const howItWorksSteps = [
    {
      step: 1,
      title: 'Choose Your Item',
      description: 'Browse our extensive catalog of skins, passes, chests, and more. Find the perfect item for yourself or a friend.',
      icon: ShoppingCart,
      color: 'bg-gradient-to-r from-purple-600 to-violet-600'
    },
    {
      step: 2,
      title: 'Add Friend & Pay',
      description: 'Add the recipient as a friend in League of Legends and complete your secure payment through our platform.',
      icon: Heart,
      color: 'bg-gradient-to-r from-fuchsia-600 to-pink-600'
    },
    {
      step: 3,
      title: 'Wait 7 Days',
      description: 'Due to Riot\'s anti-fraud policies, there\'s a mandatory 7-day waiting period before gifting is enabled.',
      icon: Clock,
      color: 'bg-gradient-to-r from-emerald-600 to-teal-600'
    },
    {
      step: 4,
      title: 'Receive Your Gift',
      description: 'Once the waiting period is over, we\'ll immediately send your item. Enjoy your new League of Legends content!',
      icon: Gift,
      color: 'bg-gradient-to-r from-orange-600 to-red-600'
    }
  ]

  const featuredItems = [
    {
      id: 1,
      name: 'Elementalist Lux',
      category: 'Ultimate Skin',
      price: '$22.95',
      originalPrice: '$27.99',
      discount: '18%',
      rarity: 'Ultimate',
      image: '/placeholder.svg'
    },
    {
      id: 2,
      name: 'Battle Pass 2025',
      category: 'Event Pass',
      price: '$9.99',
      originalPrice: '$12.99',
      discount: '23%',
      rarity: 'Limited',
      image: '/placeholder.svg'
    },
    {
      id: 3,
      name: 'Masterwork Chest Bundle',
      category: 'Chest Bundle',
      price: '$19.99',
      originalPrice: '$24.99',
      discount: '20%',
      rarity: 'Epic',
      image: '/placeholder.svg'
    }
  ]

  const testimonials = [
    {
      name: 'Alex Johnson',
      username: '@alexgamer',
      rating: 5,
      text: 'Got my Dream skin in just 7 days! Super reliable service and great prices.',
      avatar: '/placeholder.svg'
    },
    {
      name: 'Sarah Chen',
      username: '@sarahplays',
      rating: 5,
      text: 'Perfect for gifting friends. The process is smooth and customer support is amazing.',
      avatar: '/placeholder.svg'
    },
    {
      name: 'Mike Rodriguez',
      username: '@mikerift',
      rating: 5,
      text: 'Been using LoLVaults for months. Always delivers exactly what they promise.',
      avatar: '/placeholder.svg'
    }
  ]

  const faqs = [
    {
      question: 'Why do I need to wait 7 days?',
      answer: 'This is a requirement from Riot Games to prevent fraud and protect account security. We follow all official Riot policies to ensure your account stays safe.'
    },
    {
      question: 'Is this service safe for my account?',
      answer: 'Absolutely! We only use official Riot Games gifting systems. We never ask for your password or compromise your account in any way.'
    },
    {
      question: 'What if I don\'t receive my item?',
      answer: 'We guarantee delivery of all items. If there are any issues, our 24/7 support team will resolve them immediately or provide a full refund.'
    },
    {
      question: 'Can I gift to any region?',
      answer: 'We support gifting across most League of Legends regions. Check our region compatibility page or contact support for specific region questions.'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-slate-950 to-black">
        {/* Main Content */}
        <div>
          {/* Header */}
          <header className="bg-black/80 backdrop-blur-xl border-b border-purple-500/20 sticky top-0 z-40">
            <div className="flex items-center justify-between p-4">
              {/* Logo */}
              <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity cursor-pointer">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-lg flex items-center justify-center">
                  <Gem className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 bg-clip-text text-transparent">
                  LoLVaults
                </span>
              </Link>

              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
                >
                  Sign In
                </Button>
                <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white">
                  Get Started
                </Button>
              </div>
            </div>
          </header>

          {/* Hero Section */}
          <section className="relative px-6 py-32 text-center overflow-hidden h-[60vh] flex items-center">
            {/* Background Image with Fade */}
            <div
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: `
                  linear-gradient(
                    to bottom,
                    rgba(0, 0, 0, 0.8) 0%,
                    rgba(0, 0, 0, 0.85) 40%,
                    rgba(0, 0, 0, 0.9) 70%,
                    rgba(0, 0, 0, 0.95) 100%
                  ),
                  linear-gradient(
                    135deg,
                    rgba(147, 51, 234, 0.1) 0%,
                    rgba(168, 85, 247, 0.05) 25%,
                    rgba(0, 0, 0, 0.4) 50%,
                    rgba(236, 72, 153, 0.05) 75%,
                    rgba(147, 51, 234, 0.1) 100%
                  ),
                  url('/Background.jpg')
                `
              }}
            ></div>

            {/* Fade to Website Background */}
            <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black via-slate-950/90 to-transparent"></div>

            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-fuchsia-600/20 blur-3xl"></div>
            <div className="relative z-10 max-w-5xl mx-auto w-full">
              <h1 className="text-6xl md:text-8xl font-bold mb-10 bg-gradient-to-r from-white via-purple-200 to-fuchsia-200 bg-clip-text text-transparent">
                Level Up Your Game
              </h1>
              <p className="text-2xl md:text-3xl text-gray-300 mb-16 max-w-3xl mx-auto">
                Discover exclusive skins, passes, chests, and orbs. Gift your friends or treat yourself to the ultimate
                gaming experience.
              </p>

              {/* Category Quick Access */}
              <div className="max-w-2xl mx-auto mb-16">
                <div className="flex justify-center items-center w-full gap-4">
                {categories.map((category) => {
                  // Map category names to store paths
                  const categoryToPath: Record<string, string> = {
                    'Skins': '/store/skins',
                    'Chromas': '/store/chromas',
                    'Passes': '/store/passes',
                    'Chests': '/store/chests',
                    'Orbs': '/store/orbs',
                    'Bundles': '/store/bundles',
                    'TFT': '/store/tft'
                  }

                  const targetPath = categoryToPath[category.name] || '/store'

                  return (
                    <Link key={category.name} href={targetPath}>
                      <Button
                        variant="ghost"
                        size="lg"
                        className="group relative bg-transparent hover:bg-transparent border-none p-2 min-w-0"
                      >
                        <div className="flex flex-col items-center gap-3">
                          <div
                            className={`w-16 h-16 ${category.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                          >
                            <category.icon className="w-7 h-7 text-white" />
                          </div>
                          <span className="text-sm text-white group-hover:text-gray-300 transition-colors font-medium truncate">
                            {category.name}
                          </span>
                        </div>
                      </Button>
                    </Link>
                  )
                })}
                </div>
              </div>
            </div>
          </section>

          {/* How It Works Section */}
          <section className="px-6 py-20 bg-black/20">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
                <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                  Getting your League of Legends items is simple and secure. Here's our proven 4-step process:
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {howItWorksSteps.map((step, index) => (
                  <div key={step.step} className="relative">
                    <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 hover:bg-white/10 transition-all h-full">
                      <CardContent className="p-6 text-center">
                        <div
                          className={`w-16 h-16 ${step.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}
                        >
                          <step.icon className="w-8 h-8 text-white" />
                        </div>
                        <div className="text-2xl font-bold text-white mb-2">Step {step.step}</div>
                        <h3 className="text-lg font-semibold text-white mb-3">{step.title}</h3>
                        <p className="text-gray-300 text-sm leading-relaxed">{step.description}</p>
                      </CardContent>
                    </Card>
                    {index < howItWorksSteps.length - 1 && (
                      <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                        <ArrowRight className="w-8 h-8 text-purple-400" />
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-12 text-center">
                <Card className="bg-gradient-to-r from-purple-600/10 to-fuchsia-600/10 border-purple-500/30 max-w-2xl mx-auto">
                  <CardContent className="p-6">
                    <Clock className="w-12 h-12 text-fuchsia-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">Why the 7-day wait?</h3>
                    <p className="text-gray-300">
                      This waiting period is required by Riot Games' anti-fraud policies. It ensures account security and
                      prevents unauthorized gifting. We follow all official guidelines to keep your account safe!
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Featured Items */}
          <section className="px-6 py-16">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-white">Featured Items</h2>
                <Button
                  variant="outline"
                  className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
                >
                  View All
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredItems.map((item) => (
                  <Card
                    key={item.id}
                    className="bg-white/5 backdrop-blur-sm border-purple-500/20 hover:bg-white/10 transition-all group overflow-hidden"
                  >
                    <div className="relative">
                      <Image
                        src={item.image || "/placeholder.svg"}
                        alt={item.name}
                        width={300}
                        height={200}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-500">-{item.discount}</Badge>
                      <Badge className="absolute top-3 right-3 bg-purple-500/80 hover:bg-purple-500/80">
                        {item.rarity}
                      </Badge>
                    </div>
                    <CardHeader>
                      <CardTitle className="text-white">{item.name}</CardTitle>
                      <CardDescription className="text-gray-400">{item.category}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl font-bold text-white">{item.price}</span>
                          <span className="text-sm text-gray-400 line-through">{item.originalPrice}</span>
                        </div>
                        <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700">
                          <Gift className="w-4 h-4 mr-2" />
                          Gift
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* Why Choose Us Section */}
          <section className="px-6 py-20 bg-black/20">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-4xl font-bold text-white mb-4">Why Choose LoLVaults?</h2>
                <p className="text-xl text-gray-300">We're the most trusted platform for League of Legends gifting</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                  <CardContent className="p-8">
                    <Lock className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-3">100% Secure</h3>
                    <p className="text-gray-300">
                      We follow all Riot Games policies and never compromise your account security
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                  <CardContent className="p-8">
                    <Zap className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-3">Fast Delivery</h3>
                    <p className="text-gray-300">Items delivered as soon as the 7-day waiting period is complete</p>
                  </CardContent>
                </Card>

                <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                  <CardContent className="p-8">
                    <Award className="w-12 h-12 text-fuchsia-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-3">Best Prices</h3>
                    <p className="text-gray-300">Competitive pricing with regular discounts and special offers</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Testimonials Section */}
          <section className="px-6 py-16">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-white mb-4">What Our Customers Say</h2>
                <p className="text-gray-300">Join thousands of satisfied gamers who trust LoLVaults</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {testimonials.map((testimonial, index) => (
                  <Card key={index} className="bg-white/5 backdrop-blur-sm border-purple-500/20">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-4">
                        <Image
                          src={testimonial.avatar || "/placeholder.svg"}
                          alt={testimonial.name}
                          width={40}
                          height={40}
                          className="rounded-full mr-3"
                        />
                        <div>
                          <div className="font-semibold text-white">{testimonial.name}</div>
                          <div className="text-sm text-gray-400">{testimonial.username}</div>
                        </div>
                      </div>
                      <div className="flex mb-3">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-gray-300 italic">"{testimonial.text}"</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* FAQ Section */}
          <section className="px-6 py-20 bg-black/20">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
                <p className="text-gray-300">Everything you need to know about our service</p>
              </div>

              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <Card key={index} className="bg-white/5 backdrop-blur-sm border-purple-500/20">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                        <MessageCircle className="w-5 h-5 text-purple-400 mr-2" />
                        {faq.question}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </section>

          {/* Stats Section */}
          <section className="px-6 py-16">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-white mb-12">Trusted by Gamers Worldwide</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                  <div className="text-4xl font-bold text-purple-400 mb-2">1M+</div>
                  <div className="text-gray-400">Items Gifted</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-violet-400 mb-2">500K+</div>
                  <div className="text-gray-400">Happy Gamers</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-fuchsia-400 mb-2">3,326</div>
                  <div className="text-gray-400">Unique Items</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-emerald-400 mb-2">24/7</div>
                  <div className="text-gray-400">Support</div>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="px-6 py-20 bg-gradient-to-r from-purple-600/10 to-fuchsia-600/10">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold text-white mb-6">Ready to Level Up?</h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of gamers who trust LoLVaults for their League of Legends items
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white px-8 py-3"
                >
                  <Heart className="w-5 h-5 mr-2" />
                  Start Shopping
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent px-8 py-3"
                >
                  Learn More
                </Button>
              </div>
            </div>
          </section>
        </div>
    </div>
  )
}