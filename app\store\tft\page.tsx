import { StoreClient } from "@/components/store/store-client"
import { tftData, getAllStoreData } from "@/lib/store-data"

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function TftPage() {
  // Get ALL store data (API + hardcoded) for consistent sidebar counts
  const allStoreData = await getAllStoreData()

  return (
    <StoreClient
      initialItems={tftData}
      initialFilter={{ type: ['TFT'] }}
      allItems={allStoreData}
    />
  )
}
