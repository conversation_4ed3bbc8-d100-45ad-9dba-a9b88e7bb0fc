"use client"

import { use<PERSON><PERSON><PERSON>, useEffect, useRef, useState } from "react"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Gem } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { PriceFilter } from "@/components/ui/price-filter"
import { ProductCard } from "./product-card"
import { FilterCombobox, FilterOption } from "./filter-combobox"
import { FilterMenu, FilterCategory } from "./filter-menu"

import { useFilterState, StoreItem } from "@/hooks/use-filter-state"
import { useDebouncedSearch } from "@/hooks/use-debounced-search"

interface StoreClientProps {
  initialItems?: StoreItem[]
  initialFilter?: {
    type?: string[]
    rarity?: string[]
    priceRange?: [number, number]
    search?: string
  }
  allItems?: StoreItem[] // Full dataset for calculating counts
}

export function StoreClient({ initialItems = [], initialFilter, allItems }: StoreClientProps) {
  // Get current pathname for navigation highlighting
  const pathname = usePathname()

  // Use the new filter state hook
  const filterState = useFilterState(initialFilter)

  // Use debounced search
  const { searchTerm, debouncedSearchTerm, updateSearchTerm, isSearching } = useDebouncedSearch()

  // Sticky search bar state and refs
  const [isSearchBarSticky, setIsSearchBarSticky] = useState(false)
  const searchBarRef = useRef<HTMLDivElement>(null)
  const searchBarSentinelRef = useRef<HTMLDivElement>(null)

  // Update filter state when debounced search changes
  useEffect(() => {
    filterState.setSearch(debouncedSearchTerm)
  }, [debouncedSearchTerm, filterState.setSearch])

  // Intersection Observer for sticky search bar
  useEffect(() => {
    const sentinel = searchBarSentinelRef.current
    if (!sentinel) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        // When the sentinel is not intersecting (out of view), make search bar sticky
        setIsSearchBarSticky(!entry.isIntersecting)
      },
      {
        // Trigger earlier - when sentinel is 150px from the navigation bar (73px + 77px buffer)
        rootMargin: '-150px 0px 0px 0px',
        threshold: 0
      }
    )

    observer.observe(sentinel)

    return () => {
      observer.disconnect()
    }
  }, [])

  // Pagination constants
  const ITEMS_PER_PAGE = 28

  // Helper function to get rarity icon
  const getRarityIcon = (rarity: string) => {
    const rarityMap: Record<string, string> = {
      'legacy': '/Legacy.png',
      'rare': '/Rare.png',
      'epic': '/Epic.png',
      'legendary': '/Legendary.png',
      'mythic': '/Mythic.png',
      'ultimate': '/Ultimate.png',
      'exalted': '/Exalted.png',
      'transcendent': '/Transcendent.png',
      'chroma': '/Chroma.png'
    }
    // Return undefined for "Others" to show no icon
    if (rarity.toLowerCase() === 'others') {
      return undefined
    }
    return rarityMap[rarity.toLowerCase()] || '/Rare.png'
  }

  // Generate filter options with counts
  const filterOptions = useMemo(() => {
    // Use allItems for counts if available, otherwise use initialItems
    const itemsForCounts = allItems || initialItems

    const typeOptions: FilterOption[] = [
      { value: "Skin", label: "Skins", count: itemsForCounts.filter(item => item.type === "Skin").length },
      { value: "Chroma", label: "Chromas", count: itemsForCounts.filter(item => item.type === "Chroma").length },
      { value: "Pass", label: "Passes", count: itemsForCounts.filter(item => item.type === "Pass").length },
      { value: "Chest", label: "Chests", count: itemsForCounts.filter(item => item.type === "Chest").length },
      { value: "Key", label: "Keys", count: itemsForCounts.filter(item => item.type === "Key").length },
      { value: "Orb", label: "Orbs", count: itemsForCounts.filter(item => item.type === "Orb").length },
      { value: "Bundle", label: "Bundles", count: itemsForCounts.filter(item => item.type === "Bundle").length },
      { value: "TFT", label: "TFT", count: itemsForCounts.filter(item => item.type === "TFT").length },
    ]

    const rarityOptions: FilterOption[] = [
      { value: "Others", label: "Others", count: itemsForCounts.filter(item => item.rarity === "Others").length },
      { value: "Legacy", label: "Legacy", icon: "/Legacy.png", count: itemsForCounts.filter(item => item.isLegacy).length },
      { value: "Rare", label: "Rare", icon: "/Rare.png", count: itemsForCounts.filter(item => item.rarity === "Rare").length },
      { value: "Epic", label: "Epic", icon: "/Epic.png", count: itemsForCounts.filter(item => item.rarity === "Epic").length },
      { value: "Legendary", label: "Legendary", icon: "/Legendary.png", count: itemsForCounts.filter(item => item.rarity === "Legendary").length },
      { value: "Mythic", label: "Mythic", icon: "/Mythic.png", count: itemsForCounts.filter(item => item.rarity === "Mythic").length },
      { value: "Ultimate", label: "Ultimate", icon: "/Ultimate.png", count: itemsForCounts.filter(item => item.rarity === "Ultimate").length },
      { value: "Exalted", label: "Exalted", icon: "/Exalted.png", count: itemsForCounts.filter(item => item.rarity === "Exalted").length },
      { value: "Transcendent", label: "Transcendent", icon: "/Transcendent.png", count: itemsForCounts.filter(item => item.rarity === "Transcendent").length },
      { value: "Chroma", label: "Chroma", icon: "/Chroma.png", count: itemsForCounts.filter(item => item.rarity === "Chroma").length },
    ].filter(option => option.count > 0) // Only show options with items

    const sortOptions: FilterOption[] = [
      { value: "price-high", label: "Highest Price" },
      { value: "price-low", label: "Lowest Price" },
      { value: "name-asc", label: "Name A-Z" },
      { value: "name-desc", label: "Name Z-A" },
    ]

    return { typeOptions, rarityOptions, sortOptions }
  }, [initialItems, allItems])

  // Create filter categories for the new FilterMenu
  const filterCategories: FilterCategory[] = useMemo(() => [
    {
      id: 'type',
      title: 'Type',
      options: filterOptions.typeOptions,
      selected: filterState.filterState.type,
      onSelectionChange: filterState.setType
    },
    {
      id: 'rarity',
      title: 'Rarity',
      options: filterOptions.rarityOptions,
      selected: filterState.filterState.rarity,
      onSelectionChange: filterState.setRarity
    }
  ], [filterOptions, filterState.filterState.type, filterState.filterState.rarity, filterState.setType, filterState.setRarity])

  // Process items using the filter state
  const filteredItems = filterState.getFilteredItems(initialItems)
  const totalPages = filterState.getTotalPages(filteredItems, ITEMS_PER_PAGE)
  const paginatedItems = filterState.getPaginatedItems(filteredItems, ITEMS_PER_PAGE)



  return (
    <div
        className="min-h-screen relative"
        style={{
          backgroundImage: `
            linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.9)),
            linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(15, 23, 42, 0.6) 50%, rgba(0, 0, 0, 0.9) 100%),
            url('/Background.jpg')
          `,
          backgroundSize: 'cover, cover, cover',
          backgroundPosition: 'center, center, center',
          backgroundRepeat: 'no-repeat, no-repeat, no-repeat',
          backgroundAttachment: 'fixed, fixed, fixed'
        }}
      >
      {/* Header */}
      <header className="bg-black/80 backdrop-blur-xl border-b border-purple-500/20 sticky top-0 z-40">
        <div className="flex items-center justify-between p-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity cursor-pointer">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-lg flex items-center justify-center">
              <Gem className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 bg-clip-text text-transparent">
              LoLVaults
            </span>
          </Link>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              className="relative border-purple-400/50 text-purple-200 bg-transparent overflow-hidden group transition-all duration-300 hover:text-purple-100 hover:bg-purple-600/25 hover:scale-105 hover:shadow-xl hover:shadow-purple-400/35 hover:border-purple-300/70 before:absolute before:inset-0 before:bg-gradient-to-r before:from-purple-400/0 before:via-purple-400/40 before:to-purple-400/0 before:translate-x-[-100%] before:transition-transform before:duration-500 hover:before:translate-x-[100%]"
            >
              Sign In
            </Button>
            <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white">
              Get Started
            </Button>
          </div>
        </div>
      </header>

      {/* Type Navigation Buttons */}
      <div className="bg-black/40 backdrop-blur-sm border-b border-purple-500/10 sticky top-[73px] z-20">
        <div className="flex items-center justify-center gap-2 px-6 py-3 overflow-x-auto">
          {[
            { name: 'Skins', path: '/store/skins' },
            { name: 'Chromas', path: '/store/chromas' },
            { name: 'Passes', path: '/store/passes' },
            { name: 'Chests', path: '/store/chests' },
            { name: 'Keys', path: '/store/keys' },
            { name: 'Orbs', path: '/store/orbs' },
            { name: 'Bundles', path: '/store/bundles' },
            { name: 'TFT', path: '/store/tft' }
          ].map((type) => (
            <Link key={type.name} href={type.path}>
              <Button
                variant="ghost"
                size="sm"
                className={`relative whitespace-nowrap text-sm font-medium transition-all duration-300 overflow-hidden group ${
                  pathname === type.path
                    ? 'bg-purple-600/30 text-purple-200 border border-purple-400/50 hover:text-purple-100 hover:bg-purple-600/50 hover:scale-105 hover:shadow-xl hover:shadow-purple-400/40 hover:border-purple-300/70'
                    : 'text-gray-300 hover:text-white hover:bg-purple-600/25 hover:scale-105 hover:shadow-xl hover:shadow-purple-400/35 before:absolute before:inset-0 before:bg-gradient-to-r before:from-purple-400/0 before:via-purple-400/40 before:to-purple-400/0 before:translate-x-[-100%] before:transition-transform before:duration-500 hover:before:translate-x-[100%]'
                }`}
              >
                {type.name}
              </Button>
            </Link>
          ))}
        </div>
      </div>

      <div className="flex">
        {/* Sidebar - Always Open */}
        <div className="fixed inset-y-0 left-0 z-30 w-80 bg-black/90 backdrop-blur-xl border-r border-purple-500/20 top-[73px] flex flex-col">
          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto p-6 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-white mb-4">Filters</h2>
            </div>

            {/* Price Filter */}
            <div className="mb-6">
              <PriceFilter
                value={filterState.filterState.priceRange}
                onValueChange={filterState.setPriceRange}
                onReset={() => filterState.setPriceRange([0, 6000])}
                min={0}
                max={6000}
                step={50}
              />
            </div>

            {/* Filter Menu */}
            <FilterMenu
              categories={filterCategories}
            />
          </div>

          {/* Sticky Reset Button at bottom of sidebar */}
          <div className="flex-shrink-0 bg-black/95 backdrop-blur-sm border-t border-purple-500/30 p-4">
            <Button
              onClick={() => {
                filterState.setType([])
                filterState.setRarity([])
              }}
              variant="outline"
              className="w-full bg-transparent border-purple-500/50 text-white hover:bg-purple-600/20 hover:border-purple-400/70 hover:text-white"
              disabled={filterState.filterState.type.length === 0 && filterState.filterState.rarity.length === 0}
            >
              Reset Filters
            </Button>
          </div>
        </div>

        {/* Main Content - Always with sidebar margin */}
        <div className="flex-1 ml-80">
          {/* Store Content */}
          <div className="p-6 pt-8">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">Store</h1>
              <p className="text-xl text-gray-300 mb-6">
                Browse our collection of League of Legends items
              </p>

              {/* Intersection Observer Sentinel - invisible element to detect when search bar should become sticky */}
              <div ref={searchBarSentinelRef} className="h-0 w-full" />

              {/* Search and Sort Row Container - Fixed height to prevent layout shift */}
              <div className="h-[72px] mb-8 relative">
                <div
                  ref={searchBarRef}
                  className={`flex items-center gap-4 transition-opacity duration-300 ${
                    isSearchBarSticky
                      ? 'fixed top-[134px] left-80 right-0 z-30 border-b border-purple-500/10 p-6'
                      : 'absolute inset-0'
                  }`}
                  style={{
                    backgroundColor: isSearchBarSticky ? 'rgba(0, 0, 0, 0.3)' : 'transparent',
                    backdropFilter: isSearchBarSticky ? 'blur(4px)' : 'none',
                    transition: 'background-color 0.4s ease, backdrop-filter 0.4s ease'
                  }}
                >
                {/* Search Input */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search items..."
                    value={searchTerm}
                    onChange={(e) => updateSearchTerm(e.target.value)}
                    className="pl-10 bg-white/10 border-purple-500/30 text-white placeholder:text-gray-400 focus:border-purple-400/50 focus-visible:ring-2 focus-visible:ring-purple-400/20 focus-visible:ring-offset-0"
                  />
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400"></div>
                    </div>
                  )}
                </div>

                {/* Sort By Filter */}
                <div className="w-64">
                  <FilterCombobox
                    options={filterOptions.sortOptions}
                    selected={filterState.filterState.sort ? [filterState.filterState.sort] : []}
                    onSelectionChange={(selected) => filterState.setSort(selected[0] || "")}
                    title="Sort By"
                    placeholder="Select sort option..."
                    singleSelect={true}
                  />
                </div>

                {/* Clear Filters Button */}
                <Button
                  onClick={() => {
                    filterState.clearAllFilters()
                    // Also clear search and sort
                    filterState.setSearch('')
                    filterState.setSort('')
                  }}
                  variant="outline"
                  size="sm"
                  className="p-2 border-purple-500/50 bg-white/10 hover:bg-red-500/20 hover:border-red-400/70 group"
                >
                  <Image
                    src="/removefilters.svg"
                    alt="Clear filters"
                    width={20}
                    height={20}
                    className="brightness-0 invert group-hover:brightness-0 group-hover:saturate-100 group-hover:invert group-hover:sepia group-hover:hue-rotate-[340deg] transition-all duration-200"
                  />
                </Button>
              </div>
              </div>
            </div>

            {/* Products Grid */}
            {paginatedItems.length > 0 ? (
              <>
                <div className="mb-6">
                  <p className="text-gray-300">
                    Showing {((filterState.currentPage - 1) * ITEMS_PER_PAGE) + 1}-{Math.min(filterState.currentPage * ITEMS_PER_PAGE, filteredItems.length)} of {filteredItems.length} item{filteredItems.length !== 1 ? 's' : ''}
                    {initialItems.length > filteredItems.length && (
                      <span className="text-gray-500"> (filtered from {initialItems.length} total)</span>
                    )}
                  </p>
                </div>



                {/* Responsive Grid */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-0.5 mb-8">
                  {paginatedItems.map((item) => (
                    <ProductCard
                      key={item.id}
                      id={item.id}
                      name={item.name}
                      rarity={item.rarity}
                      isLegacy={item.isLegacy}
                      description={item.description}
                      imageUrl={item.imageUrl}
                      price={item.price}
                      type={item.type}
                      rarityIcon={getRarityIcon(item.rarity)}
                    />
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => filterState.setCurrentPage(Math.max(1, filterState.currentPage - 1))}
                      disabled={filterState.currentPage === 1}
                      className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 hover:text-white bg-transparent disabled:opacity-50 w-20"
                    >
                      Previous
                    </Button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum
                        if (totalPages <= 5) {
                          pageNum = i + 1
                        } else if (filterState.currentPage <= 3) {
                          pageNum = i + 1
                        } else if (filterState.currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i
                        } else {
                          pageNum = filterState.currentPage - 2 + i
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={filterState.currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => filterState.setCurrentPage(pageNum)}
                            className={
                              filterState.currentPage === pageNum
                                ? "bg-gradient-to-r from-purple-600 to-fuchsia-600 text-white w-10 h-9"
                                : "border-purple-500/30 text-purple-300 hover:bg-purple-500/20 hover:text-white bg-transparent w-10 h-9"
                            }
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => filterState.setCurrentPage(Math.min(totalPages, filterState.currentPage + 1))}
                      disabled={filterState.currentPage === totalPages}
                      className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 hover:text-white bg-transparent disabled:opacity-50 w-20"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-20">
                <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Gem className="w-12 h-12 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white mb-4">
                  {initialItems.length === 0 ? "Loading Store..." : "No items found"}
                </h2>
                <p className="text-gray-300 max-w-md mx-auto">
                  {initialItems.length === 0
                    ? "Fetching the latest League of Legends skins..."
                    : "Try adjusting your filters to find what you're looking for."
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
