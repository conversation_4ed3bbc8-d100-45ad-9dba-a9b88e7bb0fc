import { StoreItem } from "@/hooks/use-filter-state"

// Helper functions for price generation
function generatePrice(rarity: string, isLegacy: boolean): number {
  const basePrice = (() => {
    switch (rarity) {
      case 'kRare': return 520
      case 'kEpic': return 975
      case 'kLegendary': return 1350
      case 'kMythic': return 1820
      case 'kUltimate': return 3250
      case 'kExalted': return 4500
      case 'kTranscendent': return 6000
      default: return 520
    }
  })()

  return isLegacy ? Math.floor(basePrice * 1.2) : basePrice
}

function generateChromaPrice(rarityNumber: number): number {
  switch (rarityNumber) {
    case 1: return 290
    case 2: return 390
    case 3: return 490
    case 4: return 590
    case 5: return 790
    case 6: return 990
    case 7: return 1290
    default: return 290
  }
}

function getRarityString(rarityNumber: number): string {
  switch (rarityNumber) {
    case 1: return 'Rare'
    case 2: return 'Epic'
    case 3: return 'Legendary'
    case 4: return 'Mythic'
    case 5: return 'Ultimate'
    case 6: return 'Exalted'
    case 7: return 'Transcendent'
    default: return 'Others'
  }
}

// Hardcoded Pass data - Easy to edit when passes change
export const passesData: StoreItem[] = [
  {
    id: 20001,
    name: "Spirit Blossom Beyond: Act 2 Pass",
    rarity: "Others",
    isLegacy: false,
    description: "Spirit Blossom Beyond: Act 2 Pass",
    imageUrl: "/Passes/Spirit Blossom Act 2 - 1.png",
    price: 1350,
    type: "Pass"
  },
  {
    id: 20002,
    name: "Spirit Blossom Beyond: Act 2 Pass Bundle",
    rarity: "Others",
    isLegacy: false,
    description: "Spirit Blossom Beyond: Act 2 Pass Bundle",
    imageUrl: "/Passes/Spirit Blossom Act 2 - 2.png",
    price: 2650,
    type: "Pass"
  },
  {
    id: 20003,
    name: "Spirit Blossom Beyond: Act 2 Premium Pass Bundle",
    rarity: "Others",
    isLegacy: false,
    description: "Spirit Blossom Beyond: Act 2 Premium Pass Bundle",
    imageUrl: "/Passes/Spirit Blossom Act 2 - 3.png",
    price: 3950,
    type: "Pass"
  },
  {
    id: 20004,
    name: "Hall of Legends 2025 Pass",
    rarity: "Others",
    isLegacy: false,
    description: "Hall of Legends 2025 Pass",
    imageUrl: "/Passes/Hall of Legends 2025 - 1.png",
    price: 1350,
    type: "Pass"
  },
  {
    id: 20005,
    name: "Risen Legend Collection",
    rarity: "Others",
    isLegacy: false,
    description: "Risen Legend Collection",
    imageUrl: "/Passes/Hall of Legends 2025 - 2.png",
    price: 2650,
    type: "Pass"
  },
  {
    id: 20006,
    name: "Immortalized Legend Collection",
    rarity: "Others",
    isLegacy: false,
    description: "Immortalized Legend Collection",
    imageUrl: "/Passes/Hall of Legends 2025 - 3.png",
    price: 3950,
    type: "Pass"
  },
  {
    id: 20007,
    name: "Signature Immortalized Legend Collection",
    rarity: "Others",
    isLegacy: false,
    description: "Signature Immortalized Legend Collection",
    imageUrl: "/Passes/Hall of Legends 2025 - 4.png",
    price: 5250,
    type: "Pass"
  }
]

// Hardcoded data for other store items - NO PLACEHOLDERS
export const chestsData: StoreItem[] = []

export const keysData: StoreItem[] = []

export const orbsData: StoreItem[] = []

export const bundlesData: StoreItem[] = []



export const tftData: StoreItem[] = []

// Function to get all hardcoded store items (excluding skins and chromas which are fetched from API)
export function getHardcodedStoreItems(): StoreItem[] {
  return [
    ...passesData,
    ...chestsData,
    ...keysData,
    ...orbsData,
    ...bundlesData,
    ...tftData
  ]
}

// Unified function to get ALL store data (API + hardcoded) for consistent sidebar counts
export async function getAllStoreData(): Promise<StoreItem[]> {
  try {
    // Fetch skins and chromas from API
    const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json', {
      next: { revalidate: 3600 }
    })

    let apiItems: StoreItem[] = []

    if (response.ok) {
      const skinsData = await response.json()

      // Process skins
      const skins = Object.values(skinsData)
        .filter((skin: any) => !skin.isBase)
        .map((skin: any) => ({
          id: skin.id,
          name: skin.name,
          rarity: skin.rarity === 'kNoRarity' ? 'Rare' : skin.rarity.replace('k', ''),
          isLegacy: skin.isLegacy,
          description: skin.description || `${skin.name} skin`,
          imageUrl: `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${skin.splashPath.toLowerCase().replace('/lol-game-data/assets', '')}`,
          price: generatePrice(skin.rarity, skin.isLegacy),
          type: 'Skin'
        }))

      // Process chromas
      const chromas: StoreItem[] = []
      Object.values(skinsData).forEach((skin: any) => {
        if (skin.isBase || !skin.chromas || skin.chromas.length === 0) {
          return
        }

        skin.chromas.forEach((chroma: any) => {
          if (chroma.skinClassification === 'kRecolor') {
            const rarityData = chroma.rarities?.find((r: any) => r.region === 'riot')
            const rarityNumber = rarityData?.rarity || 0
            const rarityString = getRarityString(rarityNumber)

            let imageUrl = ''
            if (chroma.chromaPath) {
              const processedPath = chroma.chromaPath.toLowerCase().replace('/lol-game-data/assets', '')
              imageUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${processedPath}`
            }

            chromas.push({
              id: chroma.id,
              name: chroma.name,
              rarity: rarityString,
              isLegacy: false,
              description: chroma.description || `${chroma.name} chroma`,
              imageUrl: imageUrl,
              price: generateChromaPrice(rarityNumber),
              type: 'Chroma'
            })
          }
        })
      })

      apiItems = [...skins, ...chromas]
    }

    // Get hardcoded items
    const hardcodedItems = getHardcodedStoreItems()

    // Return combined data
    return [...apiItems, ...hardcodedItems]

  } catch (error) {
    console.error('Error fetching store data:', error)
    // Return only hardcoded items if API fails
    return getHardcodedStoreItems()
  }
}
