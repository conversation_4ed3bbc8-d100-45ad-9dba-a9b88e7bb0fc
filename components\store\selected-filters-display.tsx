"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export interface FilterCategory {
  id: string
  label: string
  filters: SelectedFilter[]
}

export interface SelectedFilter {
  id: string
  label: string
  value: string
  category: string
  icon?: string
}

interface SelectedFiltersDisplayProps {
  filters: SelectedFilter[]
  onRemoveFilter: (filterId: string, category: string) => void
  onClearCategory: (category: string) => void
  onClearAll: () => void
  className?: string
}

export function SelectedFiltersDisplay({
  filters,
  onRemoveFilter,
  onClearCategory,
  onClearAll,
  className
}: SelectedFiltersDisplayProps) {
  if (filters.length === 0) {
    return null
  }

  // Group filters by category
  const groupedFilters = filters.reduce((acc, filter) => {
    if (!acc[filter.category]) {
      acc[filter.category] = []
    }
    acc[filter.category].push(filter)
    return acc
  }, {} as Record<string, SelectedFilter[]>)

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'rarity':
        return 'Rarity'
      case 'type':
        return 'Type'

      case 'price':
        return 'Price Range'
      case 'sort':
        return 'Sort'
      default:
        return category.charAt(0).toUpperCase() + category.slice(1)
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'rarity':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'type':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30'

      case 'price':
        return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'sort':
        return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  return (
    <div className={className}>
      <div className="flex flex-wrap gap-2">
          {Object.entries(groupedFilters)
            .filter(([category]) => category !== 'sort') // Never show sort filters
            .map(([category, categoryFilters]) =>
            // Show individual filters when 3 or fewer, show count when 4 or more
            // Always show individual filters for price (single-selection category)
            categoryFilters.length <= 3 || category === 'price' ? (
              // Show individual filters
              categoryFilters.map((filter) => (
                <Badge
                  key={filter.id}
                  variant="secondary"
                  className={`${getCategoryColor(category)} flex items-center space-x-1 pr-1`}
                >
                  {filter.icon && (
                    <Image
                      src={filter.icon}
                      alt={filter.label}
                      width={12}
                      height={12}
                      className="flex-shrink-0"
                    />
                  )}
                  <span className="text-xs">{filter.label}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveFilter(filter.id, category)}
                    className="h-4 w-4 p-0 hover:bg-white/20 ml-1"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </Badge>
              ))
            ) : (
              // Show count when 4 or more filters are selected
              <Badge
                key={category}
                variant="secondary"
                className={`${getCategoryColor(category)} flex items-center space-x-1 pr-1`}
              >
                <span className="text-xs">{categoryFilters.length} Selected</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onClearCategory(category)}
                  className="h-4 w-4 p-0 hover:bg-white/20 ml-1"
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            )
          )}
        </div>
    </div>
  )
}
