import { StoreClient } from "@/components/store/store-client"
import { passesData, getAllStoreData } from "@/lib/store-data"

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

export default async function PassesPage() {
  // Get ALL store data (API + hardcoded) for consistent sidebar counts
  const allStoreData = await getAllStoreData()

  return (
    <StoreClient
      initialItems={passesData}
      initialFilter={{ type: ['Pass'] }}
      allItems={allStoreData}
    />
  )
}
