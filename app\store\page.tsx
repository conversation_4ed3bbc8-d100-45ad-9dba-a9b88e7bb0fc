import { StoreClient } from "@/components/store/store-client"
import { getAllStoreData } from "@/lib/store-data"

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)



export default async function StorePage() {
  // Get ALL store data (API + hardcoded) for both display and sidebar counts
  const allStoreData = await getAllStoreData()

  return <StoreClient initialItems={allStoreData} allItems={allStoreData} />
}